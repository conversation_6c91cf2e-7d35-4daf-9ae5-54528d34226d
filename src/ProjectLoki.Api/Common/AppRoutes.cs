namespace ProjectLoki.Api.Common;
public record EndpointPermission(string Endpoint, string Method);

public class AppRoutes
{
    public const string Separator = "/";

    public const string IdKey = "{id}";
    public const string IdValue = "id";
    public const string Lookup = "lookup";

    public abstract class Membership
    {
        public const string Path = "membership";

        public abstract class Auth
        {
            public const string Path = "auth";
            public const string FullPath = Membership.Path + "/" + Path;
            public const string Login = $"{FullPath}/login";
            public const string RefreshToken = $"{FullPath}/refresh-token";
            public const string Logout = $"{FullPath}/logout";
            public const string Register = $"{FullPath}/register";
            public const string ExternalLogin = $"{FullPath}/external-login";
        }

        public static class Users //: IRoute
        {
            private const string Path = "users";
            public const string FullPath = Membership.Path + "/" + Path;
            public const string IdPath = FullPath + Separator + IdKey;
            public const string ChangePassword = $"{FullPath}/change-password";
            public static EndpointPermission[] EndpointPermissions =>[
            
                new(FullPath,"get"),
                new(FullPath,"post"),
                new(FullPath,"put"),
                new(FullPath,"delete"),
                new(ChangePassword,"patch"),
            ];
        }
        public class Roles
        {
            private const string Path = "roles";
            public const string FullPath = Membership.Path + "/" + Path;
            internal const string IdPath = FullPath + Separator + IdKey;
        
        }
        public abstract class Permissions
        {
            private const string Path = "permissions";
            public const string FullPath = Membership.Path + "/" + Path;
            internal const string IdPath = FullPath + Separator + IdKey;
            internal const string Mine = FullPath + Separator + "mine";
            public static EndpointPermission[] EndpointPermissions =>[
            
                new(FullPath,"get"),
                new(FullPath,"post"),
                new(FullPath,"put"),
                new(FullPath,"delete"),
                new(Mine,"get"),
                
            ];
        
        }

        public class RolePermission
        {
            private const string Path = "role-permissions";
            internal const string FullPath = Membership.Path + Separator + Path;
            public const string IdPath = "membership/role-permissions/role/{roleId}/permission/{permissionId}";
            public static EndpointPermission[] EndpointPermissions =>[
                new(FullPath,"post"),
                new(FullPath,"delete"),
            ];
        }

        public class UserRoles
        {
            private const string Path = "user-roles";
            public const string FullPath = Membership.Path + "/" + Path;
            internal const string IdPath = FullPath + Separator + IdKey;
            public static EndpointPermission[] EndpointPermissions =>[
                new(FullPath,"post"),
                new(FullPath,"delete"),
            ];
        }
    }

    public static class Files
    {
        private const string Path = "files";
        public const string FullPath = Path;

        public const string IdPath = FullPath + Separator + IdKey;
    }

    public abstract class OnBoarding
    {
        private const string Path = "onboarding";
        public const string FullPath = Path;
        public const string IdPath = FullPath + Separator + IdKey;
        public const string EmployeeApproval = FullPath + Separator + "employee-approval" + Separator + IdKey;
        public const string ManagerApproval = FullPath + Separator + "manager-approval" + Separator + IdKey;
        public const string GovernorateChamberApproval = FullPath + Separator + "governorate-chamber-approval" + Separator + IdKey;
        public const string FederationChambersApproval = FullPath + Separator + "federation-chambers-approval" + Separator + IdKey;
        public const string CompanyApproval = FullPath + Separator + "company-approval" + Separator + IdKey;
        public const string Mine = FullPath + Separator + "mine";
        public const string Assign = FullPath + Separator + "assign" + Separator + IdKey;
        public static EndpointPermission[] EndpointPermissions =>[
            
            new(FullPath,"get"),
            new(FullPath,"post"),
            new(FullPath,"put"),
            new(FullPath,"delete"),
            new(FullPath,"assign"),
        ];

    }
    
    public abstract class Documents
    {
        private const string Path = "documents";
        public const string FullPath = Path;
        public const string IdPath = FullPath + Separator + IdKey;
        public static EndpointPermission[] EndpointPermissions =>[
            
            new(FullPath,"get"),
            new(FullPath,"post"),
            new(FullPath,"put"),
            new(FullPath,"delete"),
        ];
        
    }

    public abstract class Addresses
    {
        private const string Path = "addresses";
        public const string FullPath = Path;
        public const string IdPath = FullPath + Separator + IdKey;
        public static EndpointPermission[] EndpointPermissions =>[
            
            new(FullPath,"get"),
            new(FullPath,"post"),
            new(FullPath,"put"),
            new(FullPath,"delete"),
        ];
    }

    public class Lookups
    {
        private const string Path = "lookups";
        public const string FullPath = Path;
        public const string IdPath = FullPath + Separator + IdKey;
    }

    public class BusinessActivitys
    {
        private const string Path = "busuness-activities";
        public const string FullPath = Path;
        public const string IdPath = FullPath + Separator + IdKey;
    }

    public class FounderSubmissions
    {
        private const string Path = "founder-submissions";
        public const string FullPath = Path;
        public const string IdPath = FullPath + Separator + IdKey;
        public const string Approval = FullPath + Separator + "approval" + Separator + IdKey;
    }

    public class Orders
    {
        private const string Path = "orders";
        public const string FullPath = Path;
        public const string IdPath = FullPath + Separator + IdKey;
        public const string Pay = FullPath + Separator + "pay" + Separator + IdKey;
        public const string Status = FullPath + Separator + "status" + Separator + IdKey;
        public const string Confirm = FullPath + Separator + "confirm";
        public const string Register = FullPath + Separator + "register" + Separator + "{bill-id}";
    }

    public class Bills
    {
        private const string Path = "bills";
        public const string FullPath = Path;
        public const string IdPath = FullPath + Separator + IdKey;
        public const string Mine = FullPath + Separator + "mine";
    }

    public class Shareholding
    {
        private const string Path = "shareholding";
        public const string FullPath = Path;
        public const string IdPath = FullPath + Separator + IdKey;
        public const string Mine = FullPath + Separator + "mine";
        public const string SubscriptionStatement = FullPath + Separator + "subscription-statement" + Separator + IdKey;
        public static EndpointPermission[] EndpointPermissions =>[
            
            new(FullPath,"get"),
            new(FullPath,"post"),
            new(FullPath,"put"),
            new(FullPath,"delete"),
            new(FullPath,"patch"),
        ];

    }
}
