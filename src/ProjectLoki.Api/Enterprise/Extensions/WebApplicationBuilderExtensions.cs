using ProjectLoki.Api.Enterprise.CompanySubmissions.Services;
using ProjectLoki.Api.Enterprise.ShareholdingSubmissions.Services;

namespace ProjectLoki.Api.Enterprise.Extensions;

public static class WebApplicationBuilderExtensions
{
    public static WebApplicationBuilder AddEnterprise(this WebApplicationBuilder builder)
    {
        builder.Services.AddScoped<ICompanySubmissionEnricher, CompanySubmissionEnricher>();
        builder.Services.AddScoped<ICompanySubmissionCreator, CompanySubmissionCreator>();
        builder.Services.AddScoped<IShareholdingSubmissionEnricher, ShareholdingSubmissionEnricher>();
        return builder;
    }
}
