using ProjectLoki.Api.Common;

namespace ProjectLoki.Api.Enterprise.Lookups.Shared;

public abstract class LookupErrors
{
    public static Error CompanyTypeIdIsNotValid => new(nameof(CompanyTypeIdIsNotValid), "Company type id is not valid");
    public static Error CompanyNotFound => new(nameof(CompanyNotFound), "Company not found");
    public static Error GovernorateIdIsNotValid => new(nameof(GovernorateIdIsNotValid), "Governorate id is not valid");
    public static Error GovernorateNotFound => new(nameof(GovernorateNotFound), "Governorate not found");
    public static Error ShareholdingCompanyTypeIdIsNotValid => new(nameof(ShareholdingCompanyTypeIdIsNotValid), "Shareholding company type id is not valid");
    public static Error ShareholdingCompanyTypeNotFound => new(nameof(ShareholdingCompanyTypeNotFound), "Shareholding company type not found");
}
