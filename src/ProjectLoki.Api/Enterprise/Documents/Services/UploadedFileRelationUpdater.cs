using AutoMapper;
using Microsoft.EntityFrameworkCore;
using ProjectLoki.Api.Common;
using ProjectLoki.Api.Data;
using ProjectLoki.Api.Enterprise.Addresses;
using ProjectLoki.Api.Enterprise.Addresses.Data;
using ProjectLoki.Api.Enterprise.Addresses.Shared;
using ProjectLoki.Api.Enterprise.Documents.Data;
using ProjectLoki.Api.Enterprise.Documents.DocumentAdd;
using ProjectLoki.Api.Enterprise.Documents.Shared;
using ProjectLoki.Api.Enterprise.Identities;
using ProjectLoki.Api.Enterprise.Identities.Data;
using ProjectLoki.Api.Enterprise.Identities.Shared;
using ProjectLoki.Api.Enterprise.Passports.Data;
using ProjectLoki.Api.Enterprise.Passports.PassportAdd;
using ProjectLoki.Api.Enterprise.Passports.Shared;
using ProjectLoki.Api.Files.Services;

namespace ProjectLoki.Api.Enterprise.Documents.Services;

public interface IFileRelationUpdater
{
    Task<Result<Document>> DocumentUpdateAsync(DocumentRequest request, string propertyName, CancellationToken ct);
    Task<Result<Address>> AddressUpdateAsync(AddressRequest request, string propertyName, CancellationToken ct);
    Task<Result<Identity>> IdentityUpdateAsync(IdentityRequest request, string propertyName, CancellationToken ct);
    Task<Result<Passport>> PassportUpdateAsync(PassportRequest request, string propertyName, CancellationToken ct);
    Task<Result<Document>> DocumentAddAsync(DocumentRequest request, string propertyName, CancellationToken ct);
}

public class FileRelationUpdater(FlokiDbContext context, IMapper mapper, IFileReferencesUpdater fileReferencesUpdater) : IFileRelationUpdater
{
    public async Task<Result<Document>> DocumentUpdateAsync(DocumentRequest request, string propertyName, CancellationToken ct)
    {
        if (!DocumentId.TryParse(request.Id, out var documentId)) return Result<Document>.Failure(DocumentErrors.DocumentIdMustBeValid(propertyName));
        var document = await context.Documents.FirstOrDefaultAsync(d => d.Id == documentId, ct);
        if (document is null) return Result<Document>.Failure(DocumentErrors.DocumentNotFound(propertyName));

        mapper.Map(request, document);
        var result = await fileReferencesUpdater.UpdateFileReferences(request.UploadedFileIds, documentId.ToString(), propertyName, ct);
        return result.IsFailure ? Result<Document>.Failure(result.Error) : Result<Document>.Success(document);
    }

    public async Task<Result<Address>> AddressUpdateAsync(AddressRequest request, string propertyName, CancellationToken ct)
    {
        if (!AddressId.TryParse(request.Id, out var documentId)) return Result<Address>.Failure(AddressErrors.AddressIdIsNotValid);
        var address = await context.Addresses.FirstOrDefaultAsync(d => d.Id == documentId, ct);
        if (address is null) return Result<Address>.Failure(AddressErrors.AddressNotFound);

        mapper.Map(request, address);
        var result = await fileReferencesUpdater.UpdateFileReferences(request.UploadedFileIds, documentId.ToString(), propertyName, ct);
        return result.IsFailure ? Result<Address>.Failure(result.Error) : Result<Address>.Success(address);
    }

    public async Task<Result<Identity>> IdentityUpdateAsync(IdentityRequest request, string propertyName, CancellationToken ct)
    {
        if (!IdentityId.TryParse(request.Id, out var documentId)) return Result<Identity>.Failure(IdentityErrors.IdentityIdIsNotValid);
        var address = await context.Identities.FirstOrDefaultAsync(d => d.Id == documentId, ct);
        if (address is null) return Result<Identity>.Failure(IdentityErrors.IdentityNotFound);

        mapper.Map(request, address);
        var result = await fileReferencesUpdater.UpdateFileReferences(request.UploadedFileIds, documentId.ToString(), propertyName, ct);
        return result.IsFailure ? Result<Identity>.Failure(result.Error) : Result<Identity>.Success(address);
    }

    public async Task<Result<Passport>> PassportUpdateAsync(PassportRequest request, string propertyName, CancellationToken ct)
    {
        if (!PassportId.TryParse(request.Id, out var documentId)) return Result<Passport>.Failure(PassportErrors.PassportIdMustBeValid(propertyName));
        var passport = await context.Passports.FirstOrDefaultAsync(d => d.Id == documentId, ct);
        if (passport is null) return Result<Passport>.Failure(PassportErrors.PassportNotFound(propertyName));

        mapper.Map(request, passport);

        var result = await fileReferencesUpdater.UpdateFileReferences(request.UploadedFileIds, documentId.ToString(), propertyName, ct);
        return result.IsFailure ? Result<Passport>.Failure(result.Error) : Result<Passport>.Success(passport);
    }

    public async Task<Result<Document>> DocumentAddAsync(DocumentRequest documentRequest, string propertyName, CancellationToken ct)
    {
        var entity = mapper.Map<Document>(documentRequest);
        entity.Id = DocumentId.New();
        var result = await fileReferencesUpdater.UpdateFileReferences(documentRequest.UploadedFileIds, entity.Id.ToString(), propertyName, ct);
        return result.IsFailure ? Result<Document>.Failure(result.Error) : Result<Document>.Success(entity);
    }
}
