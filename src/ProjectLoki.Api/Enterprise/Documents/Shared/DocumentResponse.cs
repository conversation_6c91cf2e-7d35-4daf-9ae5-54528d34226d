namespace ProjectLoki.Api.Enterprise.Documents.Shared;

public class DocumentResponse
{
    public required string Id { get; set; }
    public string? Title { get; set; }
    public string? DocumentNo { get; set; }
    public DateTimeOffset? IssuedAt { get; set; }
    public string? From { get; set; }
    public string? To { get; set; }
    public string? Content { get; set; }

    public required bool IsApproved { get; set; }
    public string? Comment { get; set; }
    public string [] UploadedFileIds { get; set; } = [];
}
