using AutoMapper.QueryableExtensions;
using FastEndpoints;
using Microsoft.EntityFrameworkCore;
using ProjectLoki.Api.Common;
using ProjectLoki.Api.Common.Extensions;
using ProjectLoki.Api.Data;
using ProjectLoki.Api.Enterprise.ShareholdingSubmissions.Data;
using ProjectLoki.Api.Enterprise.ShareholdingSubmissions.Shared;
using ProjectLoki.Api.Enterprise.ShareholdingSubmissions.ShareholdingSubmissionAdd;
using ProjectLoki.Api.Enterprise.ShareholdingSubmissions.Services;

namespace ProjectLoki.Api.Enterprise.ShareholdingSubmissions.ShareholdingFind;

public sealed class ShareholdingFindEndpoint(FlokiDbContext context, AutoMapper.IMapper mapper, IShareholdingSubmissionEnricher shareholdingSubmissionEnricher) : EndpointWithoutRequest<IResult>
{
    public override void Configure()
    {
        Get(AppRoutes.Shareholding.IdPath);
        Policies(AppRoutes.Shareholding.FullPath.ToGetPermission());
    }

    public override async Task<IResult> ExecuteAsync(CancellationToken ct)
    {
        var id = Route<string>(AppRoutes.IdValue);
        if (string.IsNullOrWhiteSpace(id) || !ShareholdingSubmissionId.TryParse(id, out var shareholdingSubmissionId))
        {
            return ShareholdingSubmissionErrors.ShareholdingSubmissionIdMustBeValid.Problem();
        }

        var result = await context.ShareholdingSubmissions
            .Where(x => x.Id == shareholdingSubmissionId)
            .ProjectTo<ShareholdingSubmissionMineResponse>(mapper.ConfigurationProvider)
            .FirstOrDefaultAsync(ct);

        if (result is null) return Results.NotFound();

        var enrichedResult = await shareholdingSubmissionEnricher.EnrichWithUploadedFilesAsync(result, ct);

        return Results.Ok(enrichedResult);
    }
}
