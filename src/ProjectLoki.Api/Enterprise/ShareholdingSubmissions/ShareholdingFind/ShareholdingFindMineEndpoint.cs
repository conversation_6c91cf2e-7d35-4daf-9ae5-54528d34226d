using AutoMapper.QueryableExtensions;
using FastEndpoints;
using Microsoft.EntityFrameworkCore;
using ProjectLoki.Api.Common;
using ProjectLoki.Api.Common.Extensions;
using ProjectLoki.Api.Common.Middlewares;
using ProjectLoki.Api.Data;
using ProjectLoki.Api.Enterprise.ShareholdingSubmissions.Services;
using ProjectLoki.Api.Enterprise.ShareholdingSubmissions.Shared;
using IMapper = AutoMapper.IMapper;

namespace ProjectLoki.Api.Enterprise.ShareholdingSubmissions.ShareholdingFind;

public sealed class ShareholdingFindMineEndpoint(FlokiDbContext context, IMapper mapper, UserInfo userInfo, IShareholdingSubmissionEnricher shareholdingSubmissionEnricher)
    : EndpointWithoutRequest<IResult>
{
    public override void Configure()
    {
        Get(AppRoutes.Shareholding.Mine);
        Policies(AppRoutes.Shareholding.FullPath.ToGetPermission());
    }

    public override async Task<IResult> ExecuteAsync(CancellationToken ct)
    {
        var userId = userInfo.UserId;

        var result = await context.ShareholdingSubmissions
            .Where(x => x.CompanySubmission!.UserId == userId)
            .ProjectTo<ShareholdingSubmissionMineResponse>(mapper.ConfigurationProvider)
            .FirstOrDefaultAsync(ct);

        if (result is null) return Results.NotFound();

        var enrichedResult = await shareholdingSubmissionEnricher.EnrichWithUploadedFilesAsync(result, ct);

        return Results.Ok(enrichedResult);
    }
}
