using ProjectLoki.Api.Common;
using ProjectLoki.Api.Enterprise.Documents.DocumentAdd;
using ProjectLoki.Api.Enterprise.ShareholdingSubmissions.Data;

namespace ProjectLoki.Api.Enterprise.ShareholdingSubmissions.Services;

public interface IShareholdingDocumentUpdateService
{
    Task<Result<IResult>> UpdateDocumentAsync<TRequest>(
        ShareholdingSubmission submission,
        TRequest request,
        ShareholdingDocumentUpdateConfiguration configuration,
        CancellationToken cancellationToken) 
        where TRequest : class;
}
