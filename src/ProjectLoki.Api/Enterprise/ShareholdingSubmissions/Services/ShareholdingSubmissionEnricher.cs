using Microsoft.EntityFrameworkCore;
using ProjectLoki.Api.Data;
using ProjectLoki.Api.Enterprise.ShareholdingSubmissions.Shared;
using ProjectLoki.Api.Files.Data;

namespace ProjectLoki.Api.Enterprise.ShareholdingSubmissions.Services;

public interface IShareholdingSubmissionEnricher
{
    Task<ShareholdingSubmissionMineResponse> EnrichWithUploadedFilesAsync(
        ShareholdingSubmissionMineResponse response, 
        CancellationToken cancellationToken = default);
}

public sealed class ShareholdingSubmissionEnricher(FlokiDbContext context) : IShareholdingSubmissionEnricher
{
    public async Task<ShareholdingSubmissionMineResponse> EnrichWithUploadedFilesAsync(
        ShareholdingSubmissionMineResponse response, 
        CancellationToken cancellationToken = default)
    {
        var referenceIds = CollectReferenceIds(response);
        
        if (referenceIds.Length == 0) 
            return response;

        var files = await FetchUploadedFilesAsync(referenceIds, cancellationToken);
        
        if (files.Count == 0) 
            return response;

        MapFilesToDocuments(response, files);
        
        return response;
    }

    private static string[] CollectReferenceIds(ShareholdingSubmissionMineResponse response)
    {
        var referenceIds = Array.Empty<string>();

        if (response.BankReportDocument?.Id != null) 
            referenceIds = [.. referenceIds, response.BankReportDocument.Id];
            
        if (response.ShareSubscriptionFormsDocument?.Id != null) 
            referenceIds = [.. referenceIds, response.ShareSubscriptionFormsDocument.Id];
            
        if (response.FeasibilityStudyDocument.Id != null) 
            referenceIds = [.. referenceIds, response.FeasibilityStudyDocument.Id];
            
        if (response.SectoralAuthorityApprovalDocument?.Id != null) 
            referenceIds = [.. referenceIds, response.SectoralAuthorityApprovalDocument.Id];
            
        if (response.FoundersCommitteeElectionDocument.Id != null) 
            referenceIds = [.. referenceIds, response.FoundersCommitteeElectionDocument.Id];
            
        if (response.SubscriptionStatementDocument?.Id != null) 
            referenceIds = [.. referenceIds, response.SubscriptionStatementDocument.Id];
            
        if (response.SubscriptionStatementPublicationDocument?.Id != null) 
            referenceIds = [.. referenceIds, response.SubscriptionStatementPublicationDocument.Id];
            
        if (response.CloseSubscriptionStatementDocument?.Id != null) 
            referenceIds = [.. referenceIds, response.CloseSubscriptionStatementDocument.Id];

        return referenceIds;
    }

    private async Task<List<UploadedFile>> FetchUploadedFilesAsync(
        string[] referenceIds, 
        CancellationToken cancellationToken)
    {
        var jsonFilters = referenceIds.Select(UploadedFile.BuildRefIdJson).ToArray();

        return await context.UploadedFiles
            .Where(f => f.References != null && 
                       jsonFilters.Any(json => EF.Functions.JsonContains(f.References!, json)))
            .ToListAsync(cancellationToken);
    }

    private static void MapFilesToDocuments(ShareholdingSubmissionMineResponse response, IReadOnlyCollection<UploadedFile> files)
    {
        if (response.BankReportDocument != null)
            response.BankReportDocument.UploadedFileIds = GetUploadedFileIds(files, response.BankReportDocument.Id);

        if (response.ShareSubscriptionFormsDocument != null)
            response.ShareSubscriptionFormsDocument.UploadedFileIds = GetUploadedFileIds(files, response.ShareSubscriptionFormsDocument.Id);

        response.FeasibilityStudyDocument.UploadedFileIds = GetUploadedFileIds(files, response.FeasibilityStudyDocument.Id);

        if (response.SectoralAuthorityApprovalDocument != null)
            response.SectoralAuthorityApprovalDocument.UploadedFileIds = GetUploadedFileIds(files, response.SectoralAuthorityApprovalDocument.Id);

        response.FoundersCommitteeElectionDocument.UploadedFileIds = GetUploadedFileIds(files, response.FoundersCommitteeElectionDocument.Id);

        if (response.SubscriptionStatementDocument != null)
            response.SubscriptionStatementDocument.UploadedFileIds = GetUploadedFileIds(files, response.SubscriptionStatementDocument.Id);

        if (response.SubscriptionStatementPublicationDocument != null)
            response.SubscriptionStatementPublicationDocument.UploadedFileIds = GetUploadedFileIds(files, response.SubscriptionStatementPublicationDocument.Id);

        if (response.CloseSubscriptionStatementDocument != null)
            response.CloseSubscriptionStatementDocument.UploadedFileIds = GetUploadedFileIds(files, response.CloseSubscriptionStatementDocument.Id);
    }

    private static string[] GetUploadedFileIds(IReadOnlyCollection<UploadedFile> files, string documentId)
    {
        return files
            .Where(f => f.References!.Any(r => r.ReferenceId == documentId))
            .Select(f => f.Id.ToString())
            .ToArray();
    }
}
