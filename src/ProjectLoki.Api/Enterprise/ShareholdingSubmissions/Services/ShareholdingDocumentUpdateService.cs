using Microsoft.EntityFrameworkCore;
using ProjectLoki.Api.Common;
using ProjectLoki.Api.Data;
using ProjectLoki.Api.Enterprise.Documents.Data;
using ProjectLoki.Api.Enterprise.Documents.Services;
using ProjectLoki.Api.Enterprise.Documents.Shared;
using ProjectLoki.Api.Enterprise.ShareholdingSubmissions.Data;

namespace ProjectLoki.Api.Enterprise.ShareholdingSubmissions.Services;

public class ShareholdingDocumentUpdateService(FlokiDbContext context, IFileRelationUpdater fileRelationUpdater) : IShareholdingDocumentUpdateService
{
    public async Task<Result<IResult>> UpdateDocumentAsync<TRequest>(
        ShareholdingSubmission submission,
        TRequest request,
        ShareholdingDocumentUpdateConfiguration configuration,
        CancellationToken cancellationToken) 
        where TRequest : class
    {
        var documentRequest = configuration.GetDocumentRequest(request);
        var currentDocumentId = configuration.GetDocumentId(submission);

        // If no document exists, create a new one
        if (currentDocumentId is null)
        {
            var documentAddResult = await fileRelationUpdater.DocumentAddAsync(
                documentRequest, 
                configuration.DocumentPropertyName, 
                cancellationToken);
                
            if (documentAddResult.IsFailure) 
                return Result<IResult>.Failure(documentAddResult.Error);

            configuration.SetDocument(submission, documentAddResult.Data);
            configuration.SetDocumentId(submission, documentAddResult.Data.Id);
            
            await context.Documents.AddAsync(documentAddResult.Data, cancellationToken);
            context.ShareholdingSubmissions.Update(submission);
            await context.SaveChangesAsync(cancellationToken);
            
            return Result<IResult>.Success(Results.NoContent());
        }

        // Validate document ID in request
        if (string.IsNullOrWhiteSpace(documentRequest.Id) || 
            !DocumentId.TryParse(documentRequest.Id, out var documentId))
        {
            return Result<IResult>.Failure(
                DocumentErrors.DocumentIdMustBeValid(configuration.DocumentPropertyName));
        }

        // Check if document ID matches
        if (documentId != currentDocumentId)
        {
            return Result<IResult>.Failure(configuration.GetDocumentIdMismatchError());
        }

        // Verify document exists
        var document = await context.Documents.FirstOrDefaultAsync(x => x.Id == documentId, cancellationToken);
        if (document is null)
        {
            return Result<IResult>.Failure(
                DocumentErrors.DocumentNotFound(configuration.DocumentPropertyName));
        }

        // Update the document
        var documentUpdateResult = await fileRelationUpdater.DocumentUpdateAsync(
            documentRequest, 
            configuration.DocumentPropertyName, 
            cancellationToken);
            
        if (documentUpdateResult.IsFailure) 
            return Result<IResult>.Failure(documentUpdateResult.Error);

        configuration.SetDocument(submission, documentUpdateResult.Data);
        configuration.SetDocumentId(submission, documentUpdateResult.Data.Id);
        
        context.Documents.Update(documentUpdateResult.Data);
        context.ShareholdingSubmissions.Update(submission);
        await context.SaveChangesAsync(cancellationToken);

        return Result<IResult>.Success(Results.NoContent());
    }
}
