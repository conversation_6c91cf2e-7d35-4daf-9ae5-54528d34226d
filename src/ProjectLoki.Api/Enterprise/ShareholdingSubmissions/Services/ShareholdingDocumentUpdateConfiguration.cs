using ProjectLoki.Api.Common;
using ProjectLoki.Api.Enterprise.Documents.Data;
using ProjectLoki.Api.Enterprise.Documents.DocumentAdd;
using ProjectLoki.Api.Enterprise.ShareholdingSubmissions.Data;
using System.Linq.Expressions;

namespace ProjectLoki.Api.Enterprise.ShareholdingSubmissions.Services;

public class ShareholdingDocumentUpdateConfiguration
{
    public required Func<ShareholdingSubmission, DocumentId?> GetDocumentId { get; init; }
    public required Action<ShareholdingSubmission, DocumentId?> SetDocumentId { get; init; }
    public required Func<ShareholdingSubmission, Document?> GetDocument { get; init; }
    public required Action<ShareholdingSubmission, Document?> SetDocument { get; init; }
    public required Func<object, DocumentRequest> GetDocumentRequest { get; init; }
    public required string DocumentPropertyName { get; init; }
    public required Func<Error> GetDocumentIdMismatchError { get; init; }

    public static ShareholdingDocumentUpdateConfiguration ForSubscriptionStatement()
    {
        return new ShareholdingDocumentUpdateConfiguration
        {
            GetDocumentId = submission => submission.SubscriptionStatementDocumentId,
            SetDocumentId = (submission, id) => submission.SubscriptionStatementDocumentId = id,
            GetDocument = submission => submission.SubscriptionStatementDocument,
            SetDocument = (submission, document) => submission.SubscriptionStatementDocument = document,
            GetDocumentRequest = request => ((dynamic)request).SubscriptionStatementDocument,
            DocumentPropertyName = nameof(ShareholdingSubmission.SubscriptionStatementDocument),
            GetDocumentIdMismatchError = () => ShareholdingSubmissionAdd.ShareholdingSubmissionErrors.SubscriptionStatementDocumentIdMismatch
        };
    }

    public static ShareholdingDocumentUpdateConfiguration ForSubscriptionStatementPublication()
    {
        return new ShareholdingDocumentUpdateConfiguration
        {
            GetDocumentId = submission => submission.SubscriptionStatementPublicationDocumentId,
            SetDocumentId = (submission, id) => submission.SubscriptionStatementPublicationDocumentId = id,
            GetDocument = submission => submission.SubscriptionStatementPublicationDocument,
            SetDocument = (submission, document) => submission.SubscriptionStatementPublicationDocument = document,
            GetDocumentRequest = request => ((dynamic)request).SubscriptionStatementPublicationDocument,
            DocumentPropertyName = nameof(ShareholdingSubmission.SubscriptionStatementPublicationDocument),
            GetDocumentIdMismatchError = () => ShareholdingSubmissionAdd.ShareholdingSubmissionErrors.SubscriptionStatementPublicationDocumentIdMismatch
        };
    }
}
