using ProjectLoki.Api.Enterprise.CompanySubmissions.Shared;
using ProjectLoki.Api.Enterprise.Documents.Shared;
using ProjectLoki.Api.Enterprise.Lookups.Shared;
using ProjectLoki.Api.Membership.Shared;

namespace ProjectLoki.Api.Enterprise.ShareholdingSubmissions.Shared;

public class ShareholdingSubmissionResponse
{
    public required string Id { get; set; }
    public required CompanySubmissionResponse CompanySubmission { get; set; }
    public required LookupResponse ShareholdingCompanyType { get; set; }
}

public class ShareholdingSubmissionMineResponse: ShareholdingSubmissionResponse
{
    public required DocumentResponse FeasibilityStudyDocument { get; set; }
    public required DocumentResponse? SectoralAuthorityApprovalDocument { get; set; }
    public required DocumentResponse FoundersCommitteeElectionDocument { get; set; }
    public DocumentResponse? BankReportDocument { get; set; }
    public DocumentResponse? ShareSubscriptionFormsDocument { get; set; }
    public DocumentResponse? SubscriptionStatementDocument { get; set; }
    public bool? IsSubscriptionStatementApproved { get; set; }
    public DateTimeOffset? SubscriptionStatementApprovedAt { get; set; }
    public string? SubscriptionStatementComment { get; set; }

    public DocumentResponse? SubscriptionStatementPublicationDocument { get; set; }
    public bool? IsSubscriptionStatementPublicationApproved { get; set; }
    public DateTimeOffset? SubscriptionStatementPublicationApprovedAt { get; set; }
    public string? SubscriptionStatementPublicationComment { get; set; }

    public DocumentResponse? CloseSubscriptionStatementDocument { get; set; }
    public bool? IsCloseSubscriptionStatementApproved { get; set; }
    public DateTimeOffset? CloseSubscriptionStatementApprovedAt { get; set; }
    public string? CloseSubscriptionStatementComment { get; set; }

    public bool? IsFinalDocumentsApproved { get; set; }
    public DateTimeOffset? FinalDocumentsApprovedAt { get; set; }
    public string? FinalDocumentsComment { get; set; }

    public bool? IsShareholdingEmployeeApproved { get; set; }
    public DateTimeOffset? ShareholdingEmployeeApprovedAt { get; set; }

    public bool? IsShareholdingManagerApproved { get; set; }

    public DateTimeOffset? ShareholdingManagerApprovedAt { get; set; }
}

public class ShareholdingSubmissionDetailsResponse : ShareholdingSubmissionMineResponse
{
    public UserLookupResponse? SubscriptionStatementApprovedByUser { get; set; }
    public UserLookupResponse? SubscriptionStatementPublicationApprovedByUser { get; set; }
    public UserLookupResponse? CloseSubscriptionStatementApprovedByUser { get; set; }
    public UserLookupResponse? FinalDocumentsApprovedByUser { get; set; }
    public UserLookupResponse? ShareholdingEmployeeApprovedByUser { get; set; }
    public UserLookupResponse? ShareholdingManagerApprovedByUser { get; set; }
}
