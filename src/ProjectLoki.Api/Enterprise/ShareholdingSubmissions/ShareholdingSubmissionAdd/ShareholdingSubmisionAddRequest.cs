using ProjectLoki.Api.Enterprise.Addresses;
using ProjectLoki.Api.Enterprise.CompanySubmissions.CompanySubmissionAdd;
using ProjectLoki.Api.Enterprise.Documents.DocumentAdd;
using ProjectLoki.Api.Enterprise.FounderSubmissions;

namespace ProjectLoki.Api.Enterprise.ShareholdingSubmissions.ShareholdingSubmissionAdd;

public sealed record ShareholdingSubmissionAddRequest(
    string CompanyName,
    string? SuggestedCompanyName,
    string? SuggestedCompanyName2,
    string? SuggestedCompanyName3,
    string CompanyTypeId,
    string GovernorateId,
    double CapitalAmount,
    int NumberOfShares,
    string[] BusinessActivityIds,
    DocumentAddRequest FoundingContractDocument,
    DocumentAddRequest BankLetterDocument,
    DocumentAddRequest CommercialNameReservationDocument,
    AddressAddRequest Address,
    FounderSubmissionAddRequest[] Founders,
    string ShareholdingCompanyTypeId,
    DocumentAddRequest FeasibilityStudyDocument,
    DocumentAddRequest SectoralAuthorityApprovalDocument,
    DocumentAddRequest FoundersCommitteeElectionDocument) : CompanySubmissionAddRequest(CompanyName, SuggestedCompanyName, SuggestedCompanyName2, SuggestedCompanyName3, CompanyTypeId, GovernorateId,
    CapitalAmount, NumberOfShares, BusinessActivityIds, FoundingContractDocument, BankLetterDocument, CommercialNameReservationDocument, Address, Founders);
