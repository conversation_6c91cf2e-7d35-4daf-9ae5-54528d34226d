using FastEndpoints;
using Microsoft.EntityFrameworkCore;
using ProjectLoki.Api.Common;
using ProjectLoki.Api.Common.Extensions;
using ProjectLoki.Api.Data;
using ProjectLoki.Api.Enterprise.CompanySubmissions.Data;
using ProjectLoki.Api.Enterprise.CompanySubmissions.Services;
using ProjectLoki.Api.Enterprise.Documents.Data;
using ProjectLoki.Api.Enterprise.Lookups.Data;
using ProjectLoki.Api.Enterprise.Services;
using ProjectLoki.Api.Enterprise.ShareholdingSubmissions.Data;
using ProjectLoki.Api.Files.Shared;

namespace ProjectLoki.Api.Enterprise.ShareholdingSubmissions.ShareholdingSubmissionAdd;

public class ShareholdingSubmissionAddEndpoint(ICompanySubmissionCreator creator, IDocumentValidator documentValidator, FlokiDbContext context) : Endpoint<ShareholdingSubmissionAddRequest, IResult>
{
    public override void Configure()
    {
        Post(AppRoutes.Shareholding.FullPath);
        Policies(AppRoutes.Shareholding.FullPath.ToPostPermission());
    }

    public override async Task<IResult> ExecuteAsync(ShareholdingSubmissionAddRequest request, CancellationToken ct)
    {
        await using var transaction = await context.Database.BeginTransactionAsync(ct);
        var result = await creator.CreateAsync(request, true, ct);
        if (result.IsFailure) return result.Error.Problem();

        var shareholdingCompanyTypeIdResult = await ValidateShareholdingTypeId(request, ct);
        if (shareholdingCompanyTypeIdResult.IsFailure) return shareholdingCompanyTypeIdResult.Error.Problem();
        var shareholdingCompanyTypeId = shareholdingCompanyTypeIdResult.Data;

        var feasibilityStudyDocumentResult =
            await documentValidator.ValidateDocument(request.FeasibilityStudyDocument, FileErrors.FeasibilityStudyDocumentFileIdInvalid, FileErrors.FeasibilityStudyDocumentFileNotFound, ct);
        if (feasibilityStudyDocumentResult.IsFailure) return feasibilityStudyDocumentResult.Error.Problem();

        var sectoralAuthorityApprovalDocumentResult = await documentValidator.ValidateDocument(request.SectoralAuthorityApprovalDocument, FileErrors.SectoralAuthorityApprovalDocumentFileIdInvalid,
            FileErrors.SectoralAuthorityApprovalDocumentFileNotFound, ct);
        if (sectoralAuthorityApprovalDocumentResult.IsFailure) return sectoralAuthorityApprovalDocumentResult.Error.Problem();

        var foundersCommitteeElectionDocumentResult = await documentValidator.ValidateDocument(request.FoundersCommitteeElectionDocument, FileErrors.FoundersCommitteeElectionDocumentFileIdInvalid,
            FileErrors.FoundersCommitteeElectionDocumentFileNotFound, ct);
        if (foundersCommitteeElectionDocumentResult.IsFailure) return foundersCommitteeElectionDocumentResult.Error.Problem();

        var shareholdingSubmission = CreateShareholdingInstance(result, shareholdingCompanyTypeId, feasibilityStudyDocumentResult, sectoralAuthorityApprovalDocumentResult,
            foundersCommitteeElectionDocumentResult);
        await context.ShareholdingSubmissions.AddAsync(shareholdingSubmission, ct);
        UpdateCompanySubmission(result, shareholdingSubmission);

        await context.SaveChangesAsync(ct);
        await transaction.CommitAsync(ct);

        return Results.Created($"{AppRoutes.OnBoarding.FullPath}/{result.Data.Id}", result.Data.ShareholdingSubmissionId);
    }

    private async Task<Result<LookupId>> ValidateShareholdingTypeId(ShareholdingSubmissionAddRequest request, CancellationToken ct)
    {
        if (!LookupId.TryParse(request.ShareholdingCompanyTypeId, out var shareholdingCompanyTypeId)) return Result<LookupId>.Failure(ShareholdingSubmissionErrors.ShareholdingCompanyTypeIdIsNotValid);
        if (!await context.Lookups.AnyAsync(x => x.Id == shareholdingCompanyTypeId && x.LookupType == LookupTypes.ShareholdingType, ct))
            return Result<LookupId>.Failure(ShareholdingSubmissionErrors.ShareholdingCompanyTypeNotFound);
        return Result<LookupId>.Success(shareholdingCompanyTypeId);
    }

    private void UpdateCompanySubmission(Result<CompanySubmission> result, ShareholdingSubmission shareholdingSubmission)
    {
        result.Data.ShareholdingSubmissionId = shareholdingSubmission.Id;
        result.Data.ShareholdingSubmission = shareholdingSubmission;
        context.CompanySubmissions.Update(result.Data);
    }

    private static ShareholdingSubmission CreateShareholdingInstance(Result<CompanySubmission> result, LookupId shareholdingCompanyTypeId, Result<Document> feasibilityStudyDocumentResult,
        Result<Document> sectoralAuthorityApprovalDocumentResult, Result<Document> foundersCommitteeElectionDocumentResult)
    {
        var shareholdingSubmission = new ShareholdingSubmission
        {
            CompanySubmissionId = result.Data.Id,
            ShareholdingCompanyTypeId = shareholdingCompanyTypeId,
            FeasibilityStudyDocumentId = feasibilityStudyDocumentResult.Data.Id,
            FeasibilityStudyDocument = feasibilityStudyDocumentResult.Data,
            SectoralAuthorityApprovalDocumentId = sectoralAuthorityApprovalDocumentResult.Data.Id,
            SectoralAuthorityApprovalDocument = sectoralAuthorityApprovalDocumentResult.Data,
            FoundersCommitteeElectionDocumentId = foundersCommitteeElectionDocumentResult.Data.Id,
            FoundersCommitteeElectionDocument = foundersCommitteeElectionDocumentResult.Data,
            Id = ShareholdingSubmissionId.New(),
        };
        return shareholdingSubmission;
    }
}
