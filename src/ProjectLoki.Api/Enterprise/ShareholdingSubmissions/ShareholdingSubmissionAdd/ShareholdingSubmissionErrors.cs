using ProjectLoki.Api.Common;

namespace ProjectLoki.Api.Enterprise.ShareholdingSubmissions.ShareholdingSubmissionAdd;

public class ShareholdingSubmissionErrors
{
    public static Error ShareholdingCompanyTypeIdIsNotValid => new(nameof(ShareholdingCompanyTypeIdIsNotValid), "Shareholding company type id is not valid");
    public static Error ShareholdingCompanyTypeNotFound => new(nameof(ShareholdingCompanyTypeNotFound), "Shareholding company type not found");
    public static Error ShareholdingSubmissionIdMustBeValid => new(nameof(ShareholdingSubmissionIdMustBeValid), "Shareholding submission id must be valid");
    public static object SubscriptionStatementDocumentIdMistmatch { get; }
}
