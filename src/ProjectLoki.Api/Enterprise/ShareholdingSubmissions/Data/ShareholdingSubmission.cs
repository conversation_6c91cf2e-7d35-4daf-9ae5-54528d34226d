using AutoMapper;
using ProjectLoki.Api.Common;
using ProjectLoki.Api.Enterprise.CompanySubmissions.Data;
using ProjectLoki.Api.Enterprise.Documents.Data;
using ProjectLoki.Api.Enterprise.Lookups.Data;
using ProjectLoki.Api.Enterprise.ShareholdingSubmissions.Shared;
using ProjectLoki.Api.Enterprise.ShareholdingSubmissions.ShareholdingUpdate;
using ProjectLoki.Api.Membership.Data;
using Vogen;

namespace ProjectLoki.Api.Enterprise.ShareholdingSubmissions.Data;

public class ShareholdingSubmission: EntityBase<ShareholdingSubmissionId>
{
    public required CompanySubmissionId CompanySubmissionId { get; set; }
    public CompanySubmission? CompanySubmission { get; set; }
    
    public required LookupId ShareholdingCompanyTypeId { get; set; }
    public Lookup? ShareholdingCompanyType { get; set; }
    
    public DocumentId? BankReportDocumentId { get; set; }
    public Document? BankReportDocument { get; set; } 
    
    public DocumentId? ShareSubscriptionFormsDocumentId { get; set; }
    public Document? ShareSubscriptionFormsDocument { get; set; } 
    
    public required DocumentId FeasibilityStudyDocumentId { get; set; }
    public Document? FeasibilityStudyDocument { get; set; }
    
    public required DocumentId SectoralAuthorityApprovalDocumentId { get; set; }
    public Document? SectoralAuthorityApprovalDocument { get; set; }
    
    public required DocumentId FoundersCommitteeElectionDocumentId { get; set; }
    public Document? FoundersCommitteeElectionDocument { get; set; }

    #region SubscriptionStatement

    public DocumentId? SubscriptionStatementDocumentId { get; set; }
    public Document? SubscriptionStatementDocument { get; set; }
    public bool? IsSubscriptionStatementApproved { get; set; }
    public UserId? SubscriptionStatementApprovedByUserId { get; set; }
    public User? SubscriptionStatementApprovedByUser { get; set; }
    public DateTimeOffset? SubscriptionStatementApprovedAt { get; set; }
    public string? SubscriptionStatementComment { get; set; }

    #endregion

    #region SubscriptionStatementPublication

    public DocumentId? SubscriptionStatementPublicationDocumentId { get; set; }
    public Document? SubscriptionStatementPublicationDocument { get; set; }
    public bool? IsSubscriptionStatementPublicationApproved { get; set; }
    public UserId? SubscriptionStatementPublicationApprovedByUserId { get; set; }
    public User? SubscriptionStatementPublicationApprovedByUser { get; set; }
    public DateTimeOffset? SubscriptionStatementPublicationApprovedAt { get; set; }
    public string? SubscriptionStatementPublicationComment { get; set; }

    #endregion

    #region CloseSubscriptionStatement

    public DocumentId? CloseSubscriptionStatementDocumentId { get; set; }
    public Document? CloseSubscriptionStatementDocument { get; set; }
    public bool? IsCloseSubscriptionStatementApproved { get; set; }
    public UserId? CloseSubscriptionStatementApprovedByUserId { get; set; }
    public User? CloseSubscriptionStatementApprovedByUser { get; set; }
    public DateTimeOffset? CloseSubscriptionStatementApprovedAt { get; set; }
    public string? CloseSubscriptionStatementComment { get; set; }

    #endregion

    #region FinalDocuments

    public bool? IsFinalDocumentsApproved { get; set; }
    public UserId? FinalDocumentsApprovedByUserId { get; set; }
    public User? FinalDocumentsApprovedByUser { get; set; }
    public DateTimeOffset? FinalDocumentsApprovedAt { get; set; }
    public string? FinalDocumentsComment { get; set; }

    #endregion

    #region ShareholdingEmployeeApproved

    public bool? IsShareholdingEmployeeApproved { get; set; }
    public UserId? ShareholdingEmployeeApprovedByUserId { get; set; }
    public User? ShareholdingEmployeeApprovedByUser { get; set; }
    public DateTimeOffset? ShareholdingEmployeeApprovedAt { get; set; }

    #endregion

    #region ShareholdingManagerApproved

    public bool? IsShareholdingManagerApproved { get; set; }
    public UserId? ShareholdingManagerApprovedByUserId { get; set; }
    public User? ShareholdingManagerApprovedByUser { get; set; }
    public DateTimeOffset? ShareholdingManagerApprovedAt { get; set; }

    #endregion

}

[ValueObject<Ulid>]
public partial struct ShareholdingSubmissionId
{
    public static ShareholdingSubmissionId New() => From(Ulid.NewUlid());
}

public sealed class ShareholdingSubmissionProfile : Profile
{
    public ShareholdingSubmissionProfile()
    {
        CreateMap<ShareholdingSubmission, ShareholdingSubmissionResponse>();
        CreateMap<ShareholdingSubmission, ShareholdingSubmissionMineResponse>()
            .IncludeBase<ShareholdingSubmission, ShareholdingSubmissionResponse>();
        CreateMap<ShareholdingSubmission, ShareholdingSubmissionDetailsResponse>()
            .IncludeBase<ShareholdingSubmission, ShareholdingSubmissionMineResponse>();
        
        CreateMap<ShareholdingSubmissionUpdateRequest, ShareholdingSubmission>()
            .ForMember(x => x.ShareholdingCompanyTypeId, op => op.Ignore())
            .ForMember(x => x.FeasibilityStudyDocumentId, op => op.Ignore())
            .ForMember(x => x.SectoralAuthorityApprovalDocumentId, op => op.Ignore())
            .ForMember(x => x.FoundersCommitteeElectionDocumentId, op => op.Ignore())
            .ForMember(x => x.BankReportDocumentId, op => op.Ignore())
            .ForMember(x => x.ShareSubscriptionFormsDocumentId, op => op.Ignore());
        
    }
}
