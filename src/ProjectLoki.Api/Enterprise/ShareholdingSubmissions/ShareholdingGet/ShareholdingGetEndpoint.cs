using AutoMapper.QueryableExtensions;
using FastEndpoints;
using Microsoft.EntityFrameworkCore;
using ProjectLoki.Api.Common;
using ProjectLoki.Api.Common.Extensions;
using ProjectLoki.Api.Data;
using ProjectLoki.Api.Enterprise.ShareholdingSubmissions.Shared;
using IMapper = AutoMapper.IMapper;

namespace ProjectLoki.Api.Enterprise.ShareholdingSubmissions.ShareholdingGet;

public sealed record ShareholdingGetRequest : GetListRequestBase;

public sealed class ShareholdingGetEndpoint(FlokiDbContext context, IMapper mapper) : Endpoint<ShareholdingGetRequest, ShareholdingSubmissionResponse[]>
{
    public override void Configure()
    {
        Get(AppRoutes.Shareholding.FullPath);
        Policies(AppRoutes.Shareholding.FullPath.ToGetPermission());
    }

    public override async Task HandleAsync(ShareholdingGetRequest request, CancellationToken ct)
    {
        var result = await context.ShareholdingSubmissions
            .ApplySorting(request)
            .ApplyPagination(request)
            .ProjectTo<ShareholdingSubmissionResponse>(mapper.ConfigurationProvider)
            .ToArrayAsync(ct);

        await SendAsync(result, cancellation: ct);
    }
}
