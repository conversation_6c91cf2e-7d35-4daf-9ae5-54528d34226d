using FastEndpoints;
using Microsoft.EntityFrameworkCore;
using ProjectLoki.Api.Common;
using ProjectLoki.Api.Common.Extensions;
using ProjectLoki.Api.Data;
using ProjectLoki.Api.Enterprise.Documents.DocumentAdd;
using ProjectLoki.Api.Enterprise.Documents.Services;
using ProjectLoki.Api.Enterprise.Lookups.Data;
using ProjectLoki.Api.Enterprise.Lookups.Shared;
using ProjectLoki.Api.Enterprise.ShareholdingSubmissions.Data;
using ProjectLoki.Api.Enterprise.ShareholdingSubmissions.ShareholdingSubmissionAdd;
using IMapper = AutoMapper.IMapper;

namespace ProjectLoki.Api.Enterprise.ShareholdingSubmissions.ShareholdingUpdate;

public sealed record ShareholdingSubmissionUpdateRequest(
    string ShareholdingCompanyTypeId,
    DocumentRequest FeasibilityStudyDocument,
    DocumentRequest SectoralAuthorityApprovalDocument,
    DocumentRequest FoundersCommitteeElectionDocument);

public sealed class ShareholdingUpdateEndpoint(FlokiDbContext context, I<PERSON><PERSON><PERSON> mapper, IFileRelationUpdater fileRelationUpdater) : Endpoint<ShareholdingSubmissionUpdateRequest, IResult>
{
    public override void Configure()
    {
        Patch(AppRoutes.Shareholding.IdPath);
        Policies(AppRoutes.Shareholding.FullPath.ToPatchPermission());
    }

    public override async Task<IResult> ExecuteAsync(ShareholdingSubmissionUpdateRequest request, CancellationToken ct)
    {
        var id = Route<string>(AppRoutes.IdValue);
        if (string.IsNullOrWhiteSpace(id) || !ShareholdingSubmissionId.TryParse(id, out var shareholdingSubmissionId))
            return ShareholdingSubmissionErrors.ShareholdingSubmissionIdMustBeValid.Problem();

        if (string.IsNullOrWhiteSpace(request.ShareholdingCompanyTypeId) || !LookupId.TryParse(request.ShareholdingCompanyTypeId, out var shareholdingCompanyId))
            return LookupErrors.ShareholdingCompanyTypeIdIsNotValid.Problem();
        
        if (!await context.Lookups.AnyAsync(x => x.Id == shareholdingCompanyId && x.LookupType == LookupTypes.ShareholdingType, ct))
            return LookupErrors.ShareholdingCompanyTypeNotFound.Problem();
        
        var submission = await context.ShareholdingSubmissions
            .Where(x => x.Id == shareholdingSubmissionId)
            .FirstOrDefaultAsync(ct);

        if (submission is null) return Results.NotFound();

        var feasibilityStudyDocumentResult = await fileRelationUpdater.DocumentUpdateAsync(request.FeasibilityStudyDocument, nameof(ShareholdingSubmission.FeasibilityStudyDocument), ct);
        if (feasibilityStudyDocumentResult.IsFailure) return feasibilityStudyDocumentResult.Error.Problem();

        var sectoralAuthorityApprovalDocumentResult =
            await fileRelationUpdater.DocumentUpdateAsync(request.SectoralAuthorityApprovalDocument, nameof(ShareholdingSubmission.SectoralAuthorityApprovalDocument), ct);
        if (sectoralAuthorityApprovalDocumentResult.IsFailure) return sectoralAuthorityApprovalDocumentResult.Error.Problem();

        var foundersCommitteeElectionDocumentResult =
            await fileRelationUpdater.DocumentUpdateAsync(request.FoundersCommitteeElectionDocument, nameof(ShareholdingSubmission.FoundersCommitteeElectionDocument), ct);
        if (foundersCommitteeElectionDocumentResult.IsFailure) return foundersCommitteeElectionDocumentResult.Error.Problem();

        mapper.Map(request, submission);
        submission.FeasibilityStudyDocument = feasibilityStudyDocumentResult.Data;
        submission.SectoralAuthorityApprovalDocument = sectoralAuthorityApprovalDocumentResult.Data;
        submission.FoundersCommitteeElectionDocument = foundersCommitteeElectionDocumentResult.Data;
        context.Documents.UpdateRange(submission.FeasibilityStudyDocument, submission.SectoralAuthorityApprovalDocument, submission.FoundersCommitteeElectionDocument);
        context.ShareholdingSubmissions.Update(submission);
        await context.SaveChangesAsync(ct);

        return Results.NoContent();
    }
}
