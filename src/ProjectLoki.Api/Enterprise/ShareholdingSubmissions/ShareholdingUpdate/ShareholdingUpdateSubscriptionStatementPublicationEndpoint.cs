using FastEndpoints;
using FluentValidation;
using Microsoft.EntityFrameworkCore;
using ProjectLoki.Api.Common;
using ProjectLoki.Api.Common.Extensions;
using ProjectLoki.Api.Data;
using ProjectLoki.Api.Enterprise.Documents.Data;
using ProjectLoki.Api.Enterprise.Documents.DocumentAdd;
using ProjectLoki.Api.Enterprise.Documents.Services;
using ProjectLoki.Api.Enterprise.Documents.Shared;
using ProjectLoki.Api.Enterprise.ShareholdingSubmissions.Data;
using ProjectLoki.Api.Enterprise.ShareholdingSubmissions.ShareholdingSubmissionAdd;

namespace ProjectLoki.Api.Enterprise.ShareholdingSubmissions.ShareholdingUpdate;

public sealed record ShareholdingSubscriptionStatementUpdateRequest(DocumentRequest SubscriptionStatementDocument);

public sealed class ShareholdingSubscriptionStatementUpdateRequestValidator : Validator<ShareholdingSubscriptionStatementUpdateRequest>
{
    public ShareholdingSubscriptionStatementUpdateRequestValidator()
    {
        RuleFor(x => x.SubscriptionStatementDocument)
            .SetValidator(new DocumentRequestValidator())
            .NotNull();
    }
}

public sealed class ShareholdingSubscriptionStatementUpdateEndpoint(FlokiDbContext context, IFileRelationUpdater fileRelationUpdater)
    : Endpoint<ShareholdingSubscriptionStatementUpdateRequest, IResult>
{
    public override void Configure()
    {
        Patch(AppRoutes.Shareholding.SubscriptionStatement);
        Policies(AppRoutes.Shareholding.FullPath.ToPatchPermission());
    }

    public override async Task<IResult> ExecuteAsync(ShareholdingSubscriptionStatementUpdateRequest request, CancellationToken ct)
    {
        var id = Route<string>(AppRoutes.IdValue);
        if (string.IsNullOrWhiteSpace(id) || !ShareholdingSubmissionId.TryParse(id, out var shareholdingSubmissionId))
            return ShareholdingSubmissionErrors.ShareholdingSubmissionIdMustBeValid.Problem();


        var submission = await context.ShareholdingSubmissions
            .Where(x => x.Id == shareholdingSubmissionId)
            .FirstOrDefaultAsync(ct);

        if (submission is null) return Results.NotFound();

        if (submission.SubscriptionStatementDocumentId is null)
        {
            var documentAddResult = await fileRelationUpdater.DocumentAddAsync(request.SubscriptionStatementDocument, nameof(ShareholdingSubmission.SubscriptionStatementDocument), ct);
            if (documentAddResult.IsFailure) return documentAddResult.Error.Problem();
            submission.SubscriptionStatementDocument = documentAddResult.Data;
            submission.SubscriptionStatementDocumentId = documentAddResult.Data.Id;
            await context.Documents.AddAsync(submission.SubscriptionStatementDocument, ct);
            context.ShareholdingSubmissions.Update(submission);
            await context.SaveChangesAsync(ct);
            return Results.NoContent();
        }

        if(string.IsNullOrWhiteSpace(request.SubscriptionStatementDocument.Id) || !DocumentId.TryParse(request.SubscriptionStatementDocument.Id, out var documentId))
            return DocumentErrors.DocumentIdMustBeValid(nameof(ShareholdingSubmission.SubscriptionStatementDocument)).Problem();
        
        if (documentId != submission.SubscriptionStatementDocumentId)
            return ShareholdingSubmissionErrors.SubscriptionStatementDocumentIdMismatch.Problem();

        var document = await context.Documents.FirstOrDefaultAsync(x => x.Id == documentId, ct);
        if (document is null) return DocumentErrors.DocumentNotFound(nameof(ShareholdingSubmission.SubscriptionStatementDocument)).Problem();
        

        var documentUpdateResult = await fileRelationUpdater.DocumentUpdateAsync(request.SubscriptionStatementDocument, nameof(ShareholdingSubmission.FeasibilityStudyDocument), ct);
        if (documentUpdateResult.IsFailure) return documentUpdateResult.Error.Problem();

        submission.SubscriptionStatementDocument = documentUpdateResult.Data;
        submission.SubscriptionStatementDocumentId = documentUpdateResult.Data.Id;
        context.Documents.Update(submission.SubscriptionStatementDocument);
        context.ShareholdingSubmissions.Update(submission);
        await context.SaveChangesAsync(ct);

        return Results.NoContent();
    }
}
