using FastEndpoints;
using FluentValidation;
using Microsoft.EntityFrameworkCore;
using ProjectLoki.Api.Common;
using ProjectLoki.Api.Common.Extensions;
using ProjectLoki.Api.Data;
using ProjectLoki.Api.Enterprise.Documents.DocumentAdd;
using ProjectLoki.Api.Enterprise.ShareholdingSubmissions.Data;
using ProjectLoki.Api.Enterprise.ShareholdingSubmissions.Services;
using ProjectLoki.Api.Enterprise.ShareholdingSubmissions.ShareholdingSubmissionAdd;

namespace ProjectLoki.Api.Enterprise.ShareholdingSubmissions.ShareholdingUpdate;

public sealed record ShareholdingUpdateSubscriptionStatementPublicationRequest(
    DocumentRequest SubscriptionStatementPublicationDocument,
    );

public sealed class ShareholdingUpdateSubscriptionStatementPublicationRequestValidator : Validator<ShareholdingUpdateSubscriptionStatementPublicationRequest>
{
    public ShareholdingUpdateSubscriptionStatementPublicationRequestValidator()
    {
        RuleFor(x => x.SubscriptionStatementPublicationDocument)
            .SetValidator(new DocumentRequestValidator())
            .NotNull();
    }
}

public sealed class ShareholdingUpdateSubscriptionStatementPublicationEndpoint(FlokiDbContext context, IFileRelationUpdater fileRelationUpdater)
    : Endpoint<ShareholdingUpdateSubscriptionStatementPublicationRequest, IResult>
{
    public override void Configure()
    {
        Patch(AppRoutes.Shareholding.SubscriptionStatementPublication);
        Policies(AppRoutes.Shareholding.FullPath.ToPatchPermission());
    }

    public override async Task<IResult> ExecuteAsync(ShareholdingUpdateSubscriptionStatementPublicationRequest request, CancellationToken ct)
    {
        var id = Route<string>(AppRoutes.IdValue);
        if (string.IsNullOrWhiteSpace(id) || !ShareholdingSubmissionId.TryParse(id, out var shareholdingSubmissionId))
            return ShareholdingSubmissionErrors.ShareholdingSubmissionIdMustBeValid.Problem();


        var submission = await context.ShareholdingSubmissions
            .Where(x => x.Id == shareholdingSubmissionId)
            .FirstOrDefaultAsync(ct);

        if (submission is null) return Results.NotFound();

        if (submission.SubscriptionStatementPublicationDocumentId is null)
        {
            var documentAddResult = await fileRelationUpdater.DocumentAddAsync(request.SubscriptionStatementPublicationDocument, nameof(ShareholdingSubmission.SubscriptionStatementPublicationDocument), ct);
            if (documentAddResult.IsFailure) return documentAddResult.Error.Problem();
            submission.SubscriptionStatementPublicationDocument = documentAddResult.Data;
            submission.SubscriptionStatementPublicationDocumentId = documentAddResult.Data.Id;
            await context.Documents.AddAsync(submission.SubscriptionStatementPublicationDocument, ct);
            context.ShareholdingSubmissions.Update(submission);
            await context.SaveChangesAsync(ct);
            return Results.NoContent();
        }

        if(string.IsNullOrWhiteSpace(request.SubscriptionStatementPublicationDocument.Id) || !DocumentId.TryParse(request.SubscriptionStatementPublicationDocument.Id, out var documentId))
            return DocumentErrors.DocumentIdMustBeValid(nameof(ShareholdingSubmission.SubscriptionStatementPublicationDocument)).Problem();
        
        if (documentId != submission.SubscriptionStatementPublicationDocumentId)
            return ShareholdingSubmissionErrors.SubscriptionStatementPublicationDocumentIdMismatch.Problem();

        var document = await context.Documents.FirstOrDefaultAsync(x => x.Id == documentId, ct);
        if (document is null) return DocumentErrors.DocumentNotFound(nameof(ShareholdingSubmission.SubscriptionStatementPublicationDocument)).Problem();
        

        var documentUpdateResult = await fileRelationUpdater.DocumentUpdateAsync(request.SubscriptionStatementPublicationDocument, nameof(ShareholdingSubmission.FeasibilityStudyDocument), ct);
        if (documentUpdateResult.IsFailure) return documentUpdateResult.Error.Problem();

        submission.SubscriptionStatementPublicationDocument = documentUpdateResult.Data;
        submission.SubscriptionStatementPublicationDocumentId = documentUpdateResult.Data.Id;
        context.Documents.Update(submission.SubscriptionStatementPublicationDocument);
        context.ShareholdingSubmissions.Update(submission);
        await context.SaveChangesAsync(ct);

        return Results.NoContent();
    }
}
