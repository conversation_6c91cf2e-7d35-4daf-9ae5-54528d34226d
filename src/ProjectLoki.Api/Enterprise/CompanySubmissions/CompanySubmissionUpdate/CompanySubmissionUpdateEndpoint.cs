using FastEndpoints;
using Microsoft.EntityFrameworkCore;
using ProjectLoki.Api.Common;
using ProjectLoki.Api.Common.Middlewares;
using ProjectLoki.Api.Data;
using ProjectLoki.Api.Enterprise.Addresses.Data;
using ProjectLoki.Api.Enterprise.CompanySubmissions.Data;
using ProjectLoki.Api.Enterprise.CompanySubmissions.Shared;
using ProjectLoki.Api.Enterprise.Documents.Data;
using ProjectLoki.Api.Enterprise.Documents.Services;

namespace ProjectLoki.Api.Enterprise.CompanySubmissions.CompanySubmissionUpdate;

public sealed class CompanySubmissionUpdateEndpoint(
    FlokiDbContext context,
    UserInfo userInfo,
    AutoMapper.IMapper mapper,
    IFileRelationUpdater fileRelationUpdater)
    : Endpoint<CompanySubmissionUpdateRequest, IResult>
{
    public override void Configure()
    {
        Patch(AppRoutes.OnBoarding.IdPath);
    }

    public override async Task<IResult> ExecuteAsync(CompanySubmissionUpdateRequest request, CancellationToken ct)
    {
        var submissionResult = await GetCompanySubmission(ct);
        if (submissionResult.IsFailure) return submissionResult.Error.Problem();
        var submission = submissionResult.Data;
        
        var founderDocumentUpdaterResult = await fileRelationUpdater.DocumentUpdateAsync(request.FoundingContractDocument, nameof(CompanySubmission.FoundingContractDocument), ct);
        if (founderDocumentUpdaterResult.IsFailure) return founderDocumentUpdaterResult.Error.Problem();
        
        var bankLetterDocumentUpdaterResult = await fileRelationUpdater.DocumentUpdateAsync(request.BankLetterDocument, nameof(CompanySubmission.BankLetterDocument), ct);
        if (bankLetterDocumentUpdaterResult.IsFailure) return bankLetterDocumentUpdaterResult.Error.Problem();
        
        var commercialNameReservationDocumentUpdaterResult = await fileRelationUpdater.DocumentUpdateAsync(request.CommercialNameReservationDocument, nameof(CompanySubmission.CommercialNameReservationDocument), ct);
        if (commercialNameReservationDocumentUpdaterResult.IsFailure) return commercialNameReservationDocumentUpdaterResult.Error.Problem();
        
        var addressUpdaterResult = await fileRelationUpdater.AddressUpdateAsync(request.Address, nameof(CompanySubmission.Address), ct);
        if(addressUpdaterResult.IsFailure)
            return addressUpdaterResult.Error.Problem();
        
        PrepareEntityForUpdate(request, submission, founderDocumentUpdaterResult, bankLetterDocumentUpdaterResult, commercialNameReservationDocumentUpdaterResult, addressUpdaterResult);
        context.CompanySubmissions.Update(submission);
        await context.SaveChangesAsync(ct);
        return Results.NoContent();
    }

    private void PrepareEntityForUpdate(CompanySubmissionUpdateRequest request, CompanySubmission submission, Result<Document> founderDocumentUpdaterResult, Result<Document> bankLetterDocumentUpdaterResult,
        Result<Document> commercialNameReservationDocumentUpdaterResult, Result<Address> addressUpdaterResult)
    {
        mapper.Map(request, submission);
        submission.FoundingContractDocument = founderDocumentUpdaterResult.Data;
        submission.BankLetterDocument = bankLetterDocumentUpdaterResult.Data;
        submission.CommercialNameReservationDocument = commercialNameReservationDocumentUpdaterResult.Data;
        submission.Address = addressUpdaterResult.Data;
        context.Documents.UpdateRange(submission.FoundingContractDocument, submission.BankLetterDocument, submission.CommercialNameReservationDocument);
        context.Addresses.Update(submission.Address);
        
    }

    private async Task<Result<CompanySubmission>> GetCompanySubmission(CancellationToken ct)
    {
        var id = Route<string>(AppRoutes.IdValue);
        if (string.IsNullOrWhiteSpace(id) || !CompanySubmissionId.TryParse(id, out var submissionId))
        
            return Result<CompanySubmission>.Failure(CompanySubmissionErrors.CompanySubmissionIdMustBeValid);
        
        var submission = await context.CompanySubmissions
            .Where(x => x.UserId == userInfo.UserId)
            .FirstOrDefaultAsync(x => x.Id == submissionId, ct);
        return submission is null ? Result<CompanySubmission>.Failure(Error.NotFound) : Result<CompanySubmission>.Success(submission);
    }
}
