using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using ProjectLoki.Api.Common;
using ProjectLoki.Api.Common.Middlewares;
using ProjectLoki.Api.Data;
using ProjectLoki.Api.Enterprise.BusinessActivities.Data;
using ProjectLoki.Api.Enterprise.BusinessActivities.Shared;
using ProjectLoki.Api.Enterprise.CompanySubmissionBusinessActivities;
using ProjectLoki.Api.Enterprise.CompanySubmissions.CompanySubmissionAdd;
using ProjectLoki.Api.Enterprise.CompanySubmissions.Data;
using ProjectLoki.Api.Enterprise.CompanySubmissions.Shared;
using ProjectLoki.Api.Enterprise.Documents.Data;
using ProjectLoki.Api.Enterprise.Documents.DocumentAdd;
using ProjectLoki.Api.Enterprise.FounderSubmissions;
using ProjectLoki.Api.Enterprise.Lookups.Data;
using ProjectLoki.Api.Enterprise.Lookups.Shared;
using ProjectLoki.Api.Enterprise.Services;
using ProjectLoki.Api.Files.Shared;
using ProjectLoki.Api.Orders.Bills.Services;

namespace ProjectLoki.Api.Enterprise.CompanySubmissions.Services;

public interface ICompanySubmissionCreator
{
    Task<Result<CompanySubmission>> CreateAsync(CompanySubmissionAddRequest request, bool saveChanges = false, CancellationToken ct = default);
}

internal sealed class CompanySubmissionCreator(
    FlokiDbContext db,
    IMapper mapper,
    UserInfo userInfo,
    IAddressValidator addressValidator,
    IDocumentValidator documentValidator,
    IFounderSubmissionInserter founderInserter,
    IBillService billService,
    IOptionsMonitor<ApplicationConfiguration> appOptions) : ICompanySubmissionCreator
{
    public async Task<Result<CompanySubmission>> CreateAsync(CompanySubmissionAddRequest request, bool saveChanges, CancellationToken ct = default)
    {
        var preventDuplicates = appOptions.CurrentValue.PreventDuplicatedSubmissions;
        if (preventDuplicates && await db.CompanySubmissions.AnyAsync(x => x.UserId == userInfo.UserId, ct)) return Result<CompanySubmission>.Failure(CompanySubmissionErrors.AlreadySubmitted);

        var validateResult = await ValidateRequestAsync(request, ct);
        if (validateResult.IsFailure) return Result<CompanySubmission>.Failure(validateResult.Error);
        var (companyTypeId, governorateId, businessActivityIds) = validateResult.Data;


        var foundingContract = await ValidateDoc(request.FoundingContractDocument, FileErrors.FoundingContractFileIdInvalid, FileErrors.FoundingContractFileNotFound, ct);
        if (foundingContract.IsFailure) return Error(foundingContract);

        var bankLetter = await ValidateDoc(request.BankLetterDocument, FileErrors.BankLetterFileIdInvalid, FileErrors.BankLetterFileNotFound, ct);
        if (bankLetter.IsFailure) return Error(bankLetter);

        var commercialNameRes = await ValidateDoc(request.CommercialNameReservationDocument, FileErrors.CommercialNameReservationFileIdInvalid, FileErrors.CommercialNameReservationFileNotFound, ct);
        if (commercialNameRes.IsFailure) return Error(commercialNameRes);

        var address = await addressValidator.ProcessAddress(request.Address, FileErrors.CompanyAddressFileIdInvalid, FileErrors.CompanyAddressFileNotFound, ct);
        if (address.IsFailure) return Error(address);

        var entity = mapper.Map<CompanySubmission>(request);
        entity.Id = CompanySubmissionId.New();
        entity.UserId = userInfo.UserId;

        entity.CompanyTypeId = companyTypeId;
        entity.GovernorateId = governorateId;
        entity.AddressId = address.Data.Id;

        entity.FoundingContractDocumentId = foundingContract.Data.Id;
        entity.BankLetterDocumentId = bankLetter.Data.Id;
        entity.CommercialNameReservationDocumentId = commercialNameRes.Data.Id;

        entity.Address = address.Data;
        entity.FoundingContractDocument = foundingContract.Data;
        entity.BankLetterDocument = bankLetter.Data;
        entity.CommercialNameReservationDocument = commercialNameRes.Data;

        entity.CompanySubmissionBusinessActivities = businessActivityIds.Select(id => new CompanySubmissionBusinessActivity
            {
                CompanySubmissionId = entity.Id,
                BusinessActivityId = id,
            })
            .ToArray();


        var founders = await founderInserter.InsertAsync(request.Founders, entity.Id, ct);
        if (founders.IsFailure) return Error(founders);

        entity.Founders = founders.Data;
        await db.CompanySubmissions.AddAsync(entity, ct);

        var bill = await billService.CreateBillAsync(entity, ct);
        if (bill.IsFailure) return Error(bill);

        if (saveChanges) await db.SaveChangesAsync(ct);

        return Result<CompanySubmission>.Success(entity);

        Result<CompanySubmission> Error<T>(Result<T> r) where T : notnull => Result<CompanySubmission>.Failure(r.Error);

        async Task<Result<Document>> ValidateDoc(DocumentAddRequest docReq, Error idErr, Error notFoundErr, CancellationToken token) =>
            await documentValidator.ValidateDocument(docReq, idErr, notFoundErr, token);
    }


    private async Task<Result<(LookupId, LookupId, BusinessActivityId[])>> ValidateRequestAsync(CompanySubmissionAddRequest request, CancellationToken ct)
    {
        if (!LookupId.TryParse(request.CompanyTypeId, out var compTypeId)) return Fail(LookupErrors.CompanyTypeIdIsNotValid);
        if (!await db.Lookups.AnyAsync(x => x.Id == compTypeId && x.LookupType == LookupTypes.CompanyType, ct)) return Fail(LookupErrors.CompanyNotFound);

        if (!LookupId.TryParse(request.GovernorateId, out var govId)) return Fail(LookupErrors.GovernorateIdIsNotValid);
        if (!await db.Lookups.AnyAsync(x => x.Id == govId && x.LookupType == LookupTypes.Governorate, ct)) return Fail(LookupErrors.GovernorateNotFound);

        var baIds = request.BusinessActivityIds
            .Where(s => BusinessActivityId.TryParse(s, out _))
            .Select(s => BusinessActivityId.From(Ulid.Parse(s)))
            .Distinct()
            .ToArray();

        if (baIds.Length != request.BusinessActivityIds.Length) return Fail(BusinessActivityErrors.OneOrMoreBusinessActivityIdsNotValidOrDuplicated);

        var existing = await db.BusinessActivities
            .Where(x => baIds.Contains(x.Id))
            .Select(x => x.Id)
            .ToArrayAsync(ct);

        return existing.Length != baIds.Length
            ? Fail(BusinessActivityErrors.OneOrMoreBusinessActivitiesNotFound)
            : Result<(LookupId, LookupId, BusinessActivityId[])>.Success((compTypeId, govId, baIds));

        Result<(LookupId, LookupId, BusinessActivityId[])> Fail(Error e) => Result<(LookupId, LookupId, BusinessActivityId[])>.Failure(e);
    }
}
